/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：RetrievalServiceClient.java
 * 包    名：com.whiskerguard.ai.client
 * 描    述：RAG检索服务客户端
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/19
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client;

import com.whiskerguard.ai.client.dto.RetrieveRequestDTO;
import com.whiskerguard.ai.client.dto.RetrieveResponseDTO;
import feign.Request;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * RAG检索服务客户端
 * <p>
 * 通过Feign客户端调用whiskerguard-retrieval-service微服务。
 * 提供向量检索、知识查询等功能。
 *
 * 主要功能：
 * 1. 向量相似度检索
 * 2. 知识库查询
 * 3. 文档检索
 * 4. 多租户数据隔离
 *
 * <AUTHOR>
 * @since 1.0
 */
@FeignClient(name = "whiskerguard-retrieval-service", path = "/api")
public interface RetrievalServiceClient {
    /**
     * 向量相似度检索
     *
     * @param tenantId 租户ID
     * @param query 查询文本
     * @param topK 返回结果数量
     * @param threshold 相似度阈值
     * @return 检索结果
     */
    @PostMapping("/retrieval/similarity-search")
    List<Map<String, Object>> similaritySearch(
        @RequestParam("tenantId") Long tenantId,
        @RequestParam("query") String query,
        @RequestParam(value = "topK", defaultValue = "10") Integer topK,
        @RequestParam(value = "threshold", defaultValue = "0.7") Double threshold
    );

    /**
     * 知识库查询
     *
     * @param request 查询请求
     * @return 查询结果
     */
    @PostMapping("/retrieval/knowledge-query")
    Map<String, Object> queryKnowledge(@RequestBody Map<String, Object> request);

    /**
     * 法规检索
     *
     * @param tenantId 租户ID
     * @param regulationId 法规ID
     * @param industryType 行业类型
     * @return 法规内容
     */
    @GetMapping("/retrieval/regulations")
    Map<String, Object> retrieveRegulations(
        @RequestParam("tenantId") Long tenantId,
        @RequestParam(value = "regulationId", required = false) String regulationId,
        @RequestParam(value = "industryType", required = false) String industryType
    );

    /**
     * 制度检索
     *
     * @param tenantId 租户ID
     * @param companyId 企业ID
     * @param policyType 制度类型
     * @return 制度内容
     */
    @GetMapping("/retrieval/policies")
    Map<String, Object> retrievePolicies(
        @RequestParam("tenantId") Long tenantId,
        @RequestParam("companyId") Long companyId,
        @RequestParam(value = "policyType", required = false) String policyType
    );

    /**
     * 合同检索
     *
     * @param tenantId 租户ID
     * @param contractId 合同ID
     * @param contractType 合同类型
     * @return 合同内容
     */
    @GetMapping("/retrieval/contracts")
    Map<String, Object> retrieveContracts(
        @RequestParam("tenantId") Long tenantId,
        @RequestParam(value = "contractId", required = false) String contractId,
        @RequestParam(value = "contractType", required = false) String contractType
    );

    /**
     * 行业最佳实践检索
     *
     * @param tenantId 租户ID
     * @param industryType 行业类型
     * @param practiceType 实践类型
     * @return 最佳实践
     */
    @GetMapping("/retrieval/best-practices")
    Map<String, Object> retrieveBestPractices(
        @RequestParam("tenantId") Long tenantId,
        @RequestParam("industryType") String industryType,
        @RequestParam(value = "practiceType", required = false) String practiceType
    );

    /**
     * 通用检索接口
     *
     * @param tenantId 租户ID
     * @param request 检索请求
     * @return 检索结果
     */
    @PostMapping("/retrieval/retrieve")
    RetrieveResponseDTO retrieve(@RequestParam("tenantId") String tenantId, @RequestBody RetrieveRequestDTO request);

    /**
     * 健康检查
     *
     * @return 健康状态
     */
    @GetMapping("/health")
    Map<String, Object> healthCheck();
}
