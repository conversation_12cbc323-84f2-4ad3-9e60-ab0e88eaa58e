/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：SensitiveWordClient.java
 * 包    名：com.whiskerguard.ai.client
 * 描    述：猫伯伯合规管家公共微服务：提供敏感词过滤服务，用于检测和过滤内容中的敏感词、违禁词，
 *         支持单条内容过滤、批量内容过滤、详细敏感词信息返回等功能
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/6/11
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.ai.client;

import com.whiskerguard.ai.client.UserFeignClientInterceptor;
import com.whiskerguard.ai.client.dto.SensitiveWordFilterRequestDTO;
import com.whiskerguard.ai.client.dto.SensitiveWordFilterResponseDTO;
import feign.Request;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 敏感词过滤服务客户端接口
 * <p>
 * 该接口使用 Spring Cloud OpenFeign 实现微服务间的远程调用，
 * 用于与敏感词过滤服务（whiskerguard-general-service）进行通信。
 * 提供内容敏感词检测和过滤功能，保障内容安全合规。
 *
 * 配置说明：
 * - 使用 UserFeignClientInterceptor 自动传递认证信息
 * - 配置了自定义的 Feign 配置类，优化超时设置
 */
@FeignClient(name = "whiskerguardgeneralservice", configuration = SensitiveWordClient.SensitiveWordClientConfiguration.class)
public interface SensitiveWordClient {
    /**
     * 过滤内容中的敏感词接口
     * <p>
     * 将传入的文本内容中的敏感词替换为 *，保障内容合规。
     * 敏感词库基于系统配置的最新词库，支持多种敏感词类型。
     *
     * @param content 需要过滤的文本内容
     * @return 过滤后的文本内容，敏感词已被替换为 *
     */
    @PostMapping("/api/sensitive-words/filter")
    String filterContent(@RequestBody String content);

    /**
     * 过滤内容中的敏感词并返回详细信息接口
     * <p>
     * 不仅过滤敏感词，还返回原文、过滤后文本、发现的敏感词列表等详细信息。
     * 适用于需要展示敏感词详情或进行内容审核的场景。
     *
     * @param content 需要过滤的文本内容
     * @return 敏感词过滤响应DTO，包含原文、过滤后文本和发现的敏感词详情
     */
    @PostMapping("/api/sensitive-words/filter-detailed")
    SensitiveWordFilterResponseDTO filterContentDetailed(@RequestBody String content);

    /**
     * 批量过滤多个内容中的敏感词接口
     * <p>
     * 一次请求处理多条内容的敏感词过滤，提高处理效率。
     * 每条内容都会返回详细的过滤结果和敏感词信息。
     *
     * @param request 敏感词过滤请求DTO，包含多条需要过滤的内容
     * @return 敏感词过滤响应DTO列表，每条对应一个内容项的过滤结果
     */
    @PostMapping("/api/sensitive-words/batch-filter")
    List<SensitiveWordFilterResponseDTO> batchFilterContent(@RequestBody SensitiveWordFilterRequestDTO request);

    /**
     * 检查内容是否包含敏感词接口
     * <p>
     * 快速检测文本是否包含敏感词，不进行替换操作。
     * 适用于内容预检查或提交前验证场景。
     *
     * @param content 需要检查的文本内容
     * @return 布尔值，true表示包含敏感词，false表示不包含
     */
    @PostMapping("/api/sensitive-words/contains")
    Boolean containsSensitiveWords(@RequestBody String content);

    /**
     * 敏感词过滤服务客户端专用配置类
     *
     * 配置认证拦截器和优化的超时设置
     */
    @Configuration
    class SensitiveWordClientConfiguration {

        @Bean
        public UserFeignClientInterceptor userFeignClientInterceptor() {
            return new UserFeignClientInterceptor();
        }

        @Bean
        public Request.Options feignRequestOptions() {
            // 设置连接超时和读取超时 - 优化为高性能配置
            return new Request.Options(
                2000, // 连接超时 2 秒 - 敏感词过滤通常响应较快
                5000 // 读取超时 5 秒 - 为批量处理预留足够时间
            );
        }
    }
}
