/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-ai-service
 * 文件名称：RegulatoryServiceClient.java
 * 包    名：com.whiskerguard.ai.client
 * 描    述：法规制度服务客户端接口
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/1/20
 * 版本信息：1.0
 * =============================================================================
 */

package com.whiskerguard.ai.client;

import com.whiskerguard.ai.client.dto.ComplianceChecklistDTO;
import com.whiskerguard.ai.client.dto.InternalPolicyDTO;
import com.whiskerguard.ai.client.dto.LegalRegulationDTO;
import com.whiskerguard.ai.client.dto.RegulationSearchRequestDTO;
import com.whiskerguard.ai.service.dto.LawArticleDetailDTO;
import com.whiskerguard.ai.service.dto.LawReferenceDTO;
import com.whiskerguard.ai.service.dto.LawSummaryDTO;
import feign.Request;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 法规制度服务客户端接口
 * <p>
 * 该接口使用 Spring Cloud OpenFeign 实现微服务间的远程调用，
 * 用于与法规制度服务（whiskerguard-regulatory-service）进行通信。
 * 提供法律法规查询、企业内部制度管理、合规检查等功能。
 * <p>
 * 主要功能：
 * 1. 根据合同类型查询相关法律法规
 * 2. 根据行业查询适用的法规制度
 * 3. 获取企业内部制度和政策
 * 4. 提供合规检查清单
 * 5. 支持法规内容搜索
 */
@FeignClient(name = "whiskerguardregulatoryservice", configuration = RegulatoryServiceClient.RegulatoryServiceClientConfiguration.class)
public interface RegulatoryServiceClient {
    /**
     * 根据合同类型查询相关法律法规
     * <p>
     * 获取特定合同类型需要遵守的法律法规列表，
     * 用于合同审查时的合规性检查。
     *
     * @param contractType 合同类型（如：采购合同、销售合同、服务合同等）
     * @param tenantId     租户ID，用于多租户数据隔离
     * @return 相关法律法规列表
     */
    @GetMapping("/regulations/contract-type/{tenantId}")
    List<LegalRegulationDTO> getRegulationsByContractType(@RequestParam("contractType") String contractType, @PathVariable("tenantId") Long tenantId);

    /**
     * 根据行业查询相关法规
     * <p>
     * 获取特定行业需要遵守的专门法规和行业标准，
     * 用于行业特定的合规性检查。
     *
     * @param industry 行业类别（如：金融、医疗、教育、制造业等）
     * @param tenantId 租户ID，用于多租户数据隔离
     * @return 行业相关法规列表
     */
    @GetMapping("/industry/{tenantId}")
    List<LegalRegulationDTO> getRegulationsByIndustry(@RequestParam("industry") String industry, @PathVariable("tenantId") Long tenantId);

    /**
     * 查询企业内部制度
     * <p>
     * 获取企业内部的制度、政策和规范，
     * 用于确保合同符合企业内部管理要求。
     *
     * @param tenantId 租户ID，用于多租户数据隔离
     * @param category 制度类别（可选），如：CONTRACT、FINANCE、HR等
     * @return 企业内部制度列表
     */
    @GetMapping("/api/internal-policies/tenant/{tenantId}")
    List<InternalPolicyDTO> getInternalPolicies(
        @PathVariable("tenantId") Long tenantId,
        @RequestParam(value = "category", required = false) String category
    );

    /**
     * 根据关键词搜索相关法规
     * <p>
     * 基于关键词在法规数据库中进行全文搜索，
     * 用于查找与特定条款或概念相关的法规条文。
     *
     * @param searchRequest 搜索请求对象，包含关键词、租户ID等信息
     * @return 匹配的法规列表
     */
    @PostMapping("/api/regulations/search")
    List<LegalRegulationDTO> searchRegulations(@RequestBody RegulationSearchRequestDTO searchRequest);

    /**
     * 获取合规检查清单
     * <p>
     * 根据合同类型和行业获取标准化的合规检查清单，
     * 用于指导合同审查的重点检查项目。
     *
     * @param tenantId     租户ID，用于多租户数据隔离
     * @param contractType 合同类型
     * @param industry     行业类别（可选）
     * @return 合规检查清单
     */
    @GetMapping("/api/compliance/checklist")
    ComplianceChecklistDTO getComplianceChecklist(
        @RequestParam("tenantId") Long tenantId,
        @RequestParam("contractType") String contractType,
        @RequestParam(value = "industry", required = false) String industry
    );

    /**
     * 获取法规条文详情
     * <p>
     * 根据法规ID获取详细的法规条文内容，
     * 用于深入分析特定法规的具体要求。
     *
     * @param regulationId 法规ID
     * @param tenantId     租户ID，用于多租户数据隔离
     * @return 法规详细信息
     */
    @GetMapping("/api/regulations/{regulationId}")
    LegalRegulationDTO getRegulationDetail(@PathVariable("regulationId") Long regulationId, @RequestParam("tenantId") Long tenantId);

    /**
     * 检查合同条款合规性
     * <p>
     * 将合同条款与法规数据库进行比对，
     * 识别可能存在的合规风险。
     *
     * @param tenantId      租户ID
     * @param contractType  合同类型
     * @param clauseContent 条款内容
     * @return 合规性检查结果
     */
    @PostMapping("/api/compliance/check-clause")
    ComplianceChecklistDTO checkClauseCompliance(
        @RequestParam("tenantId") Long tenantId,
        @RequestParam("contractType") String contractType,
        @RequestBody String clauseContent
    );

    /**
     * 根据关键词查找相关法规
     * <p>
     * 用于AI响应后处理，根据AI响应中的关键词
     * 查找相关的法律法规和内部制度。
     *
     * @param keywords 关键词列表
     * @param tenantId 租户ID，用于多租户数据隔离
     * @return 相关法规引用列表
     */
    @PostMapping("/api/regulations/search-by-keywords")
    List<LawReferenceDTO> findRelevantRegulations(@RequestBody List<String> keywords, @RequestParam("tenantId") Long tenantId);

    /**
     * 根据业务场景获取法规摘要
     * <p>
     * 根据业务上下文获取相关的法规摘要信息，
     * 用于提示词增强和AI响应优化。
     *
     * @param businessContext 业务场景上下文
     * @param tenantId        租户ID，用于多租户数据隔离
     * @return 法规摘要列表
     */
    @GetMapping("/api/regulations/summaries")
    List<LawSummaryDTO> getRelevantLawSummaries(
        @RequestParam("businessContext") String businessContext,
        @RequestParam("tenantId") Long tenantId
    );

    /**
     * 获取具体法规条款详情（待实现）
     * <p>
     * 获取特定法规条款的详细信息，
     * 用于前端展示完整的法规内容。
     *
     * @param regulationId 法规ID
     * @param articleId    条款ID
     * @param tenantId     租户ID，用于多租户数据隔离
     * @return 法规条款详情
     */
    @GetMapping("/api/regulations/{regulationId}/articles/{articleId}")
    LawArticleDetailDTO getArticleDetail(
        @PathVariable("regulationId") Long regulationId,
        @PathVariable("articleId") Long articleId,
        @RequestParam("tenantId") Long tenantId
    );

    /**
     * 批量获取法规引用信息
     * <p>
     * 根据引用ID列表批量获取法规引用的详细信息，
     * 用于AI响应中的法规引用展示。
     *
     * @param referenceIds 引用ID列表
     * @param tenantId     租户ID，用于多租户数据隔离
     * @return 法规引用信息列表
     */
    @PostMapping("/api/regulations/batch-references")
    List<LawReferenceDTO> getBatchReferences(@RequestBody List<Long> referenceIds, @RequestParam("tenantId") Long tenantId);

    /**
     * 法规制度服务客户端专用配置类
     * <p>
     * 配置认证拦截器和超时设置，
     * 优化法规服务调用的性能和可靠性。
     */
    @Configuration
    class RegulatoryServiceClientConfiguration {

        /**
         * 配置用户认证拦截器
         * 自动传递JWT token和用户信息
         */
        @Bean
        public UserFeignClientInterceptor userFeignClientInterceptor() {
            return new UserFeignClientInterceptor();
        }

        /**
         * 配置Feign请求选项
         * 法规查询可能涉及大量数据，需要较长的超时时间
         */
        @Bean
        public Request.Options feignRequestOptions() {
            return new Request.Options(
                3000, // 连接超时 3 秒
                10000 // 读取超时 10 秒 - 法规查询可能需要更长时间
            );
        }
    }
}
